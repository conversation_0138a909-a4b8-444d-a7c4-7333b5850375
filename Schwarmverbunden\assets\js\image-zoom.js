// Image Zoom Functionality
document.addEventListener('DOMContentLoaded', function() {
    // Create modal HTML structure
    const modal = document.createElement('div');
    modal.className = 'image-zoom-modal';
    modal.id = 'imageZoomModal';
    
    modal.innerHTML = `
        <span class="image-zoom-close" id="imageZoomClose">&times;</span>
        <img class="image-zoom-content" id="imageZoomContent" alt="Vergrößertes Bild">
    `;
    
    document.body.appendChild(modal);
    
    // Get modal elements
    const zoomModal = document.getElementById('imageZoomModal');
    const zoomContent = document.getElementById('imageZoomContent');
    const closeBtn = document.getElementById('imageZoomClose');
    
    // Find all profile images that should be zoomable
    const profileImages = document.querySelectorAll('.closing-profile-image');
    
    // Add click event to each profile image
    profileImages.forEach(function(img) {
        img.addEventListener('click', function() {
            openZoom(this.src, this.alt);
        });
    });
    
    // Function to open zoom modal
    function openZoom(imageSrc, imageAlt) {
        zoomContent.src = imageSrc;
        zoomContent.alt = imageAlt;
        zoomModal.classList.add('active');
        document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }
    
    // Function to close zoom modal
    function closeZoom() {
        zoomModal.classList.remove('active');
        document.body.style.overflow = ''; // Restore scrolling
    }
    
    // Close modal when clicking the X button
    closeBtn.addEventListener('click', closeZoom);
    
    // Close modal when clicking outside the image
    zoomModal.addEventListener('click', function(e) {
        if (e.target === zoomModal) {
            closeZoom();
        }
    });
    
    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && zoomModal.classList.contains('active')) {
            closeZoom();
        }
    });
    
    // Prevent image click from closing modal
    zoomContent.addEventListener('click', function(e) {
        e.stopPropagation();
    });
});
